"""
回归测试页面模块

提供批量推理与评测功能，支持文件上传、参数配置、并发处理等功能。
"""

import asyncio
import os
from multiprocessing import Process
from pathlib import Path

import aiohttp
import pandas as pd
import streamlit as st

from frontend.base_unit import HOST_PROJECT_DIR
from frontend.output_unit import tail_run_log
from scripts.val_batch_call import load_json_info_for_process, process_batch, save_dict_to_excel
from util.cache_util import (
    CacheManager,
    RegressionTestConfig,
    get_default_regression_config,
    merge_config_with_file_columns
)
from util.common_util import may_have_bad_markdown
from util.date_util import get_cur_time_str
from util.file_util import join_path, is_valid_path, mkdir_if_not_exists
from util.process_util import FilePrintRunner, is_process_running, kill_process_and_children
from util.send_msg_tofeishu import feishu_robot_send_msg, st_gen_url


def regression_test(project_dir):
    """回归测试主函数"""
    # 创建主要布局
    sidebar = st.sidebar
    main_content = st.container()

    # 初始化目录和配置
    tmp_dir, inputs_dir, results_dir = _setup_directories(project_dir)
    cache_manager, cached_config = _setup_cache_and_config(project_dir)

    # 渲染侧边栏配置
    with sidebar:
        env = _render_sidebar_config(cached_config, cache_manager)

    with main_content:
        st.title("批量推理与评测工具")

        # 处理文件上传和显示
        available_columns, total_rows, cached_config = _handle_file_upload_and_display(
            project_dir, inputs_dir, cached_config
        )

        # 数据行选择配置
        start_row, end_row = _render_row_selection(total_rows, cached_config)

        # 配置参数
        batch_size = st.number_input("每次并发请求的数量 (batch_size)", min_value=1, max_value=30,
                                     value=cached_config.batch_size)
        val_score_flag = st.checkbox("是否评测分数 (val_score_flag)", value=cached_config.val_score_flag)

        # 列选择配置
        column_config = _render_column_selection(available_columns, cached_config)
        product_str, query_str, ref_ans, extra_columns, extra_columns_list = column_config

        # 高级配置参数
        timeout, max_retries, version = _render_advanced_config(cached_config)

        # 文件路径配置
        custom_suffix = st.text_input("自定义文件名后缀 (custom_suffix)", value=cached_config.custom_suffix)
        timestamp = get_cur_time_str()
        cur_input_file_name = os.path.basename(st.session_state["uploaded_file_path"])
        output_file_path = join_path(results_dir,
                                     f"{timestamp}_{cur_input_file_name}_F{start_row}T{end_row}_{custom_suffix}_detail.xlsx")
        val_res_file_path = join_path(results_dir,
                                      f"{timestamp}_{cur_input_file_name}_F{start_row}T{end_row}_{custom_suffix}_report.xlsx")
        log_path = join_path(tmp_dir, "file_runner.log")

        # 任务控制
        _handle_task_control(
            project_dir, tmp_dir, results_dir, log_path, cache_manager,
            start_row, end_row, batch_size, val_score_flag, product_str, query_str, ref_ans,
            extra_columns, extra_columns_list, timeout, max_retries, version, custom_suffix,
            output_file_path, val_res_file_path, env, total_rows
        )


def _setup_directories(project_dir):
    """创建必要的目录结构"""
    tmp_dir = join_path(project_dir, "tmp")
    inputs_dir = join_path(tmp_dir, "inputs")
    results_dir = join_path(tmp_dir, "results")

    mkdir_if_not_exists(tmp_dir)
    mkdir_if_not_exists(inputs_dir)
    mkdir_if_not_exists(results_dir)

    return tmp_dir, inputs_dir, results_dir


def _setup_cache_and_config(project_dir):
    """初始化缓存管理器和配置"""
    cache_manager = CacheManager(project_dir)
    cached_config = cache_manager.load_config("regression_test", RegressionTestConfig)
    if cached_config is None:
        cached_config = get_default_regression_config()
    return cache_manager, cached_config


def _render_sidebar_config(cached_config, cache_manager):
    """渲染侧边栏配置"""
    ENV_NAME_DICT = {"本地": "local", "测试": "test", "预发": "preview", "生产": "prod"}
    env_options = ["预发", "本地", "测试"]

    default_env_index = 0
    if cached_config.env in env_options:
        default_env_index = env_options.index(cached_config.env)

    env = st.selectbox("选择环境", env_options, index=default_env_index, key='env_regression_test')
    env = ENV_NAME_DICT[env]

    # 添加缓存管理按钮
    st.subheader("配置管理")
    if st.button("清除缓存配置", help="清除保存的页面配置，下次将使用默认值"):
        cache_manager.clear_config("regression_test")
        st.success("缓存配置已清除！")
        st.rerun()

    return env


def _handle_file_upload_and_display(project_dir, inputs_dir, cached_config):
    """处理文件上传和显示"""
    # 设置默认文件
    default_file_path = join_path(project_dir, 'test/test_data/国际促销员Copilot线下评测集v1.0-by中国区Copilot_2.xlsx')
    st.session_state["uploaded_file_path"] = default_file_path
    st.info(f"默认使用的文件: test/test_data/国际促销员Copilot线下评测集v1.0-by中国区Copilot_2.xlsx")

    # 文件上传
    uploaded_file = st.file_uploader("上传要处理的文件 (Excel 格式)", type=["xlsx"])
    if uploaded_file:
        input_file_path = join_path(inputs_dir, uploaded_file.name)
        with open(input_file_path, "wb") as f:
            f.write(uploaded_file.getbuffer())
        st.session_state["uploaded_file_path"] = input_file_path
        st.success(f"文件已上传: {input_file_path}, 当前正在使用的文件: {st.session_state['uploaded_file_path']}")

    # 读取和显示文件信息
    current_file_path = st.session_state.get("uploaded_file_path")
    available_columns = []
    total_rows = 0

    if current_file_path and os.path.exists(current_file_path):
        df = pd.read_excel(current_file_path)
        available_columns = df.columns.tolist()
        total_rows = len(df)
        st.dataframe(df.iloc[:10], hide_index=True)

        if available_columns:
            st.info(f"文件包含以下列: {', '.join(available_columns)}")
            st.info(f"文件总行数: {total_rows} 行")
            # 根据文件列信息更新缓存配置
            cached_config = merge_config_with_file_columns(cached_config, available_columns)
            # 更新 end_row 默认值
            if cached_config.end_row is None or cached_config.end_row > total_rows:
                cached_config.end_row = total_rows
        else:
            st.warning("无法读取文件列名，请检查文件格式")

    return available_columns, total_rows, cached_config


def _render_row_selection(total_rows, cached_config):
    """渲染数据行选择配置"""
    if total_rows > 0:
        st.subheader("数据行选择")
        col1, col2 = st.columns(2)

        with col1:
            start_row = st.number_input(
                "起始行号 (从1开始)",
                min_value=1,
                max_value=total_rows,
                value=max(1, min(cached_config.start_row, total_rows)),
                help=f"选择要测试的数据起始行号，范围: 1-{total_rows}"
            )

        with col2:
            end_row = st.number_input(
                "结束行号 (包含)",
                min_value=1,
                max_value=total_rows,
                value=max(1, min(cached_config.end_row or total_rows, total_rows)),
                help=f"选择要测试的数据结束行号，范围: 1-{total_rows}"
            )

        # 验证行号范围
        if start_row > end_row:
            st.error("起始行号不能大于结束行号！")
        else:
            selected_rows_count = end_row - start_row + 1
            st.info(f"已选择 {selected_rows_count} 行数据进行测试 (第 {start_row} 行到第 {end_row} 行)")
    else:
        start_row = 1
        end_row = 1

    return start_row, end_row


def _render_column_selection(available_columns, cached_config):
    """渲染列选择配置"""
    if available_columns:
        # 产品名称列选择
        default_product_idx = 0
        if cached_config.product_str in available_columns:
            default_product_idx = available_columns.index(cached_config.product_str)
        elif "SPU" in available_columns:
            default_product_idx = available_columns.index("SPU")
        product_str = st.selectbox("选择产品名称列 (必须提供)",
                                   options=available_columns,
                                   index=default_product_idx,
                                   key="product_column")

        # 问题列选择
        default_query_idx = 0
        if cached_config.query_str in available_columns:
            default_query_idx = available_columns.index(cached_config.query_str)
        else:
            for col in available_columns:
                if "问题" in col and ("印尼" in col or "idn" in col.lower()):
                    default_query_idx = available_columns.index(col)
                    break
        query_str = st.selectbox("选择问题列 (必须提供)",
                                 options=available_columns,
                                 index=default_query_idx,
                                 key="query_column")

        # 参考答案列选择（可选）
        ref_ans_options = ["不选择"] + available_columns
        default_ref_idx = 0
        if cached_config.ref_ans and cached_config.ref_ans in available_columns:
            default_ref_idx = available_columns.index(cached_config.ref_ans) + 1  # +1 因为有"不选择"选项
        else:
            for col in available_columns:
                if ("人工答案" in col and "印尼" in col) or ("参考答案" in col and "印尼" in col) or (
                        "答案" in col and ("idn" in col.lower() or "印尼" in col)):
                    default_ref_idx = available_columns.index(col) + 1  # +1 因为有"不选择"选项
                    break
        ref_ans_selection = st.selectbox("选择参考答案列 (可选，不选择则不进行打分)",
                                         options=ref_ans_options,
                                         index=default_ref_idx,
                                         key="ref_ans_column")
        ref_ans = ref_ans_selection if ref_ans_selection != "不选择" else ""

        # 额外保留列选择（多选）
        used_columns = [product_str, query_str]
        if ref_ans:
            used_columns.append(ref_ans)
        remaining_columns = [col for col in available_columns if col not in used_columns]

        # 使用缓存的额外列选择，但需要过滤掉不存在的列
        cached_extra_columns = [col for col in cached_config.extra_columns_list if col in remaining_columns]
        if not cached_extra_columns:  # 如果缓存中没有有效的额外列，则默认选择所有剩余列
            cached_extra_columns = remaining_columns

        extra_columns_list = st.multiselect("选择希望额外保留的列 (多选)",
                                            options=remaining_columns,
                                            default=cached_extra_columns,
                                            key="extra_columns")
        extra_columns = ",".join(extra_columns_list)
    else:
        # 如果没有可用列，回退到文本输入模式
        st.warning("无法读取文件列名，请手动输入列名")
        product_str = st.text_input("产品名称列名 (product_str)必须提供", value=cached_config.product_str)
        query_str = st.text_input("问题列名 (query_str)必须提供", value=cached_config.query_str or "问题-idn")
        ref_ans = st.text_input("参考答案列名 (ref_ans)非必须，不提供不进行打分", value=cached_config.ref_ans)
        extra_columns = st.text_input("希望额外保留的列（多列以英文逗号,隔开）",
                                      value=",".join(
                                          cached_config.extra_columns_list) if cached_config.extra_columns_list else "细分类,问题-zh",
                                      help="例如：列1,列2,列3。这些列的数据将会在输出文件中保留")
        extra_columns_list = extra_columns.split(',') if extra_columns else []

    return product_str, query_str, ref_ans, extra_columns, extra_columns_list


def _render_advanced_config(cached_config):
    """渲染高级配置参数"""
    with st.expander("高级配置"):
        timeout = st.number_input("请求超时时间 (秒)", min_value=10, max_value=300, value=cached_config.timeout)
        max_retries = st.number_input("失败请求重试次数", min_value=0, max_value=5, value=cached_config.max_retries)
        version = st.number_input("参数版本 (version)", min_value=0, max_value=1, value=cached_config.version)

    return timeout, max_retries, version


def _handle_task_control(project_dir, tmp_dir, results_dir, log_path, cache_manager,
                         start_row, end_row, batch_size, val_score_flag, product_str, query_str, ref_ans,
                         extra_columns, extra_columns_list, timeout, max_retries, version, custom_suffix,
                         output_file_path, val_res_file_path, env, total_rows):
    """处理任务控制相关功能"""
    # 初始化运行状态
    if "is_running" not in st.session_state:
        st.session_state["is_running"] = True  # 默认禁用运行按钮

    def disable_run_button():
        st.session_state["is_running"] = True

    # 运行按钮
    if st.button("运行", disabled=st.session_state["is_running"], on_click=disable_run_button):
        if not st.session_state["uploaded_file_path"]:
            st.error("请先上传文件！")
        elif total_rows > 0 and start_row > end_row:
            st.error("起始行号不能大于结束行号！")
        else:
            # 保存当前配置到缓存
            current_config = RegressionTestConfig(
                env=st.session_state.get('env_regression_test', '预发'),
                start_row=start_row,
                end_row=end_row,
                batch_size=batch_size,
                val_score_flag=val_score_flag,
                product_str=product_str,
                query_str=query_str,
                ref_ans=ref_ans,
                extra_columns_list=extra_columns_list,
                timeout=timeout,
                max_retries=max_retries,
                version=version,
                custom_suffix=custom_suffix
            )
            if cache_manager.save_config("regression_test", current_config):
                st.success("✅ 配置已保存，下次进入页面将自动加载这些设置")

            st.session_state["is_running"] = True
            st.info(f"开始处理，请稍候..., 结束后请下载{output_file_path}")
            runner = FilePrintRunner(process_file_worker, log_path)
            process = Process(target=runner, args=(st.session_state['uploaded_file_path'],
                                                   output_file_path, val_res_file_path,
                                                   batch_size, val_score_flag,
                                                   product_str, query_str, ref_ans, env,
                                                   project_dir, timeout, max_retries, version, extra_columns,
                                                   start_row, end_row))
            process.start()
            pid_file_path = join_path(tmp_dir, "current_process.pid")
            with open(pid_file_path, "w") as pid_file:
                pid_file.write(str(process.pid))
            st.success(f"已启动进程处理文件: {st.session_state['uploaded_file_path']}，进程 PID: {process.pid}")
            # 只有当 project_dir 为/home/<USER>/workspace/inference 时才执行
            if project_dir == HOST_PROJECT_DIR:
                feishu_robot_send_msg(
                    f"评测提示：开始处理文件{st.session_state['uploaded_file_path']}了, 进程 PID: {process.pid}")

    # 任务状态查询
    _render_task_status_controls(tmp_dir, project_dir)

    # 文件下载
    _render_file_download(results_dir)

    # 日志显示
    if is_valid_path(log_path):
        tail_run_log(log_path)


def _render_task_status_controls(tmp_dir, project_dir):
    """渲染任务状态控制"""
    st.info("请先查询是否可运行, 再点击运行按钮, 点击两次")
    if st.button("查询任务状态"):
        process_pid_path = join_path(tmp_dir, "current_process.pid")
        if os.path.exists(process_pid_path):
            with open(process_pid_path, "r") as pid_file:
                pid = int(pid_file.read().strip())
            if is_process_running(pid):
                st.success(f"当前任务正在运行，PID: {pid}")
                st.session_state["is_running"] = True
            else:
                st.info(f"当前任务已完成或不存在，PID: {pid}")
                st.session_state["is_running"] = False
        else:
            st.info("当前任务已完成或不存在")
            st.session_state["is_running"] = False

    # 终止任务按钮
    if st.button("终止任务"):
        process_pid_path = join_path(tmp_dir, "current_process.pid")
        if not os.path.exists(process_pid_path):
            st.info("没有找到正在运行的任务")
            return

        with open(process_pid_path, "r") as pid_file:
            pid = int(pid_file.read().strip())

        if not is_process_running(pid):
            st.info("没有找到正在运行的任务")
            return

        try:
            kill_process_and_children(pid)
            st.success(f"成功终止任务，PID: {pid}")
            # 只有当 project_dir 为/home/<USER>/workspace/inference 时才执行
            if project_dir == HOST_PROJECT_DIR:
                feishu_robot_send_msg(f"评测提示：进程 PID: {pid}被kill了, 评测任务已终止")
            st.session_state["is_running"] = False
        except ProcessLookupError:
            st.error(f"无法终止任务，PID: {pid} 不存在")


def _render_file_download(results_dir):
    """渲染文件下载功能"""
    # 提供推理评测结果文件下载 - 只显示 results 目录中的文件
    if st.session_state['is_running'] is False:
        available_files = sorted(
            [f.name for f in Path(results_dir).iterdir() if f.is_file() and f.name.endswith('.xlsx')],
            reverse=True)
        if available_files:
            selected_file = st.selectbox("选择要下载的结果文件", available_files)
            # 根据选择的文件提供下载按钮
            selected_file_path = join_path(results_dir, selected_file)
            with open(selected_file_path, "rb") as f:
                file_data = f.read()
            st.download_button(
                label=f"下载 {selected_file}",
                data=file_data,
                file_name=selected_file,
                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            )
        else:
            st.info("暂无可下载的结果文件")


def process_file_worker(input_file_path, output_file_path, val_res_file_path,
                        batch_size, val_score_flag,
                        product_str, query_str, ref_ans, env,
                        project_dir=None, timeout=60, max_retries=2, version=None, extra_columns="",
                        start_row=1, end_row=None):
    """文件处理工作函数"""
    # 加载数据
    qa_list, columns_to_read, extra_col_names, columns_info = load_json_info_for_process(
        input_file_path, product_str, query_str, ref_ans, extra_columns
    )

    # 应用行选择过滤
    qa_list = _apply_row_filtering(qa_list, start_row, end_row)

    # 准备列顺序
    ahead_cols = [product_str, query_str, ref_ans] + extra_col_names

    # 批量处理（每批处理完就保存）
    all_results = _process_batches(qa_list, batch_size, env, val_score_flag, max_retries, timeout, version,
                                   columns_info, output_file_path, ahead_cols)

    # 最终保存详细结果（确保最新数据已保存）
    save_dict_to_excel(all_results, output_file_path, ahead_cols)
    print(f"所有批次处理完成，最终结果已保存到 {output_file_path}")

    # 生成评测报告
    _generate_evaluation_report(all_results, qa_list, val_res_file_path, env)

    # 发送完成通知
    _send_completion_notification(project_dir, input_file_path, output_file_path)


def _apply_row_filtering(qa_list, start_row, end_row):
    """应用行选择过滤"""
    if end_row is None:
        end_row = len(qa_list)

    # 转换为0基索引 (用户输入是1基索引)
    start_idx = start_row - 1
    end_idx = end_row

    # 验证索引范围
    if start_idx < 0:
        start_idx = 0
    if end_idx > len(qa_list):
        end_idx = len(qa_list)
    if start_idx >= end_idx:
        print(f"警告：无效的行范围 {start_row}-{end_row}，将使用全部数据")
        start_idx = 0
        end_idx = len(qa_list)

    # 切片数据
    qa_list = qa_list[start_idx:end_idx]
    print(f"已选择第 {start_row} 行到第 {end_row} 行的数据，共 {len(qa_list)} 行")

    return qa_list


def _process_batches(qa_list, batch_size, env, val_score_flag, max_retries, timeout,version, columns_info,
                     output_file_path, ahead_cols):
    """批量处理数据，每处理完一批就保存一次"""
    total_batches = (len(qa_list) + batch_size - 1) // batch_size
    all_results = []

    async def run_batches():
        async with aiohttp.ClientSession() as session:
            for batch_num in range(total_batches):
                start_idx = batch_num * batch_size
                end_idx = min((batch_num + 1) * batch_size, len(qa_list))
                batch_qa_list = qa_list[start_idx:end_idx]
                batch_results = await process_batch(session, batch_qa_list, env, val_score_flag,
                                                    batch_num + 1, max_retries=max_retries, timeout=timeout,
                                                    version=version, columns_info=columns_info)
                all_results.extend(batch_results)

                # 每处理完一批就保存一次
                save_dict_to_excel(all_results, output_file_path, ahead_cols)
                print(f'正在处理第 {batch_num + 1} 批，共 {total_batches} 批，进度 {(batch_num + 1) / total_batches:.1%}，已保存到 {output_file_path}')

    asyncio.run(run_batches())
    return all_results


def _calculate_statistics(all_results, qa_list):
    """计算统计数据"""
    # 首Token时间统计
    first_token_results = [r for r in all_results if r.get("first_token_time") != ""]
    first_token_count = len(first_token_results)
    first_token_avg = sum(
        r.get("first_token_time", 0) for r in first_token_results) / first_token_count if first_token_count > 0 else 0

    # 内部推理时间统计
    inner_time_results = [r for r in all_results if r.get("first_token_time") != "" and r.get("infer_time_inner") != ""]
    inner_time_count = len(inner_time_results)
    inner_time_avg = sum(
        r.get("infer_time_inner", 0) for r in inner_time_results) / inner_time_count if inner_time_count > 0 else 0

    # 体感推理时间统计
    feel_time_results = [r for r in all_results if r.get("first_token_time") != "" and r.get("推理时间") != ""]
    feel_time_count = len(feel_time_results)
    feel_time_avg = sum(r.get("推理时间", 0) for r in feel_time_results) / feel_time_count if feel_time_count > 0 else 0

    # 评分统计
    score_2_count = sum(1 for r in all_results if r.get("score") == 2)
    score_1_count = sum(1 for r in all_results if r.get("score") == 1)
    accuracy_A = score_2_count / len(all_results) if all_results else 0
    accuracy_B = (score_2_count + score_1_count) / len(all_results) if all_results else 0

    # 成功率
    infer_success_rate = first_token_count / len(qa_list) if len(qa_list) > 0 else 0

    # Token消耗统计
    token_results = [r for r in all_results if r.get("total_tokens") != ""]
    token_count = len(token_results)
    token_avg = sum(r.get("total_tokens", 0) for r in token_results) / token_count if token_count > 0 else 0

    # 回答长度统计
    response_results = [r for r in all_results if r.get("推理模型答案-印尼") != ""]
    response_count = len(response_results)
    response_length_avg = sum(
        len(r.get("推理模型答案-印尼", "")) for r in response_results) / response_count if response_count > 0 else 0

    return {
        'first_token_avg': first_token_avg,
        'inner_time_avg': inner_time_avg,
        'feel_time_avg': feel_time_avg,
        'accuracy_A': accuracy_A,
        'accuracy_B': accuracy_B,
        'infer_success_rate': infer_success_rate,
        'first_token_count': first_token_count,
        'token_avg': token_avg,
        'response_length_avg': response_length_avg
    }


def _generate_evaluation_report(all_results, qa_list, val_res_file_path, env):
    """生成评测报告"""
    st.info("生成评测结果...")

    # 计算统计数据
    stats = _calculate_statistics(all_results, qa_list)

    # 检测疑似 Markdown 语法
    for result in all_results:
        answer = result.get("推理模型答案-印尼", "")
        if may_have_bad_markdown(answer):
            result["疑似 markdown"] = 1
        else:
            result["疑似 markdown"] = 0

    # 统计疑似 Markdown 的数量
    count_markdown = sum(1 for result in all_results if result.get("疑似 markdown") == 1)

    # 获取模型版本
    model_version = ""
    for result in all_results:
        if result.get("model_version") != "":
            model_version = result.get("model_version")
            break

    # 生成评测报告
    val_res = [{
        "AI打分准确率(仅2分)": f"{stats['accuracy_A']:.2%}",
        "AI打分准确率(2分和1分)": f"{stats['accuracy_B']:.2%}",
        "平均首Token时间": f"{stats['first_token_avg']:.2f}s",
        "平均infer时间": f"{stats['inner_time_avg']:.2f}s",
        "体感平均infer时间": f"{stats['feel_time_avg']:.2f}s",
        "infer_success_rate": f"{stats['infer_success_rate']:.2%}",
        "infer_success": stats['first_token_count'],
        "infer_counts": len(qa_list),
        "infer_fail": len(qa_list) - stats['first_token_count'],
        "平均token消耗": f"{stats['token_avg']:.2f}",
        "平均回答长度（字符）": f"{stats['response_length_avg']:.2f}",
        "疑似 markdown": count_markdown,
        "model_version": model_version,
        "推理环境": env,
    }]

    save_dict_to_excel(val_res, val_res_file_path)


def _send_completion_notification(project_dir, input_file_path, output_file_path):
    """发送完成通知"""
    st_gen_url(output_file_path)
    dw_url = "http://************:443/regression"

    # 只有当 project_dir 为指定路径时才执行
    if project_dir == HOST_PROJECT_DIR:
        feishu_robot_send_msg(f"评测提示：{input_file_path}评测处理完成了! 评测任务已结束, 下载页面: {dw_url}")

    st.success(f"{input_file_path}, 处理完成！")
