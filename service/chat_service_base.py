import asyncio
import datetime
import json
import time
import traceback
import uuid
from asyncio import TaskGroup

from fastapi import Request
from prometheus_client import Counter, Histogram
from config.model_config import MODEL_VERSION
from config.common_config import get_env_by_key
from config.run_config import RUN_CONFIG_DICT, DATA_TABLE_NAME
from config.chat_config import REFUSAL_MESSAGE_DICT, CANDIDATE_ITEM_SIZE, PRE_MESSAGE_DICT
from core.schema.chat_request import ChatRequest
from core.schema.chat_response import EventType, ChatResponse, ChatResponseData
from core.schema.chat_base import MessageType, IntentType
from core.schema.constant import CHAT
from core.schema.knowledge_source import KnowledgeSource
from core.schema.db_record import DBRecord
from service.base_service import BaseService
from service.model_manager import ModelManager
from service.prompt_build_service import PromptBuildService
from service.query_parse_service import QueryParseService
from util.common_util import decode_sse, get_cur_millis, is_empty
from util.mysql_db_manager import DBManager
from util.string_util import to_json_str
from core.processor import normalize_item_name

import random

ENV_NAME = get_env_by_key("ENV_NAME", "local")


class ChatServiceBase(BaseService):
    def __init__(self, logger, counter: Counter, timer: Histogram, api_key_text, query_parse_service: QueryParseService,
                 prompt_build_service: PromptBuildService, model_manager: ModelManager, normalized_item_name_list,
                 item_name_xiaomi_list, db: DBManager, name_item_dict):
        super().__init__(logger, counter, timer)
        self._api_key_text = api_key_text
        self._query_parse_service: QueryParseService = query_parse_service
        self._prompt_build_service: PromptBuildService = prompt_build_service
        self._model_manager: ModelManager = model_manager
        self._normalized_item_name_list = normalized_item_name_list
        self._item_name_xiaomi_list = item_name_xiaomi_list
        self._db = db
        self._name_item_dict = name_item_dict
        self._data_table_name = RUN_CONFIG_DICT.get(ENV_NAME).get(DATA_TABLE_NAME)
        # ToDo(hm): 存一堆中间结果不是个好的实践，后面不好拆解
        self._actual_item_name_list = []
        self._answer_intent = ""
        self._second_tags = []
        self._second_tags_map_dict = None
        self._task_filter = None
        self._task_answer_first_event = None
        self._task_retrieval_minet = None
        self._task_retrieval_doc = None
        self._task_retrieval_faq = None
        self._user_prompt = ""
        self._candidate_item_size = CANDIDATE_ITEM_SIZE

    async def chat(self, chat_request: ChatRequest, http_request: Request):
        answer_start_time = None
        first_token_elapse = 0
        response_id = str(uuid.uuid4())
        async for chat_response in self.chat_inner(chat_request, http_request):
            if chat_response.event == EventType.START_EVENT:
                answer_start_time = get_cur_millis()
                first_token_elapse = answer_start_time - chat_request.request_receive_time
                self._logger.debug(f"开始回答耗时：{first_token_elapse / 1000:.2f} 秒，response_id={response_id}")
                self._timer.labels(object="first_token_elapse", condition=chat_response.data.answer_type.name).observe(
                    first_token_elapse)
            chat_response_data = chat_response.data
            chat_response_data.answer_start_time = answer_start_time
            chat_response.conversation_id = chat_request.conversation_id
            # 生成一个 request id 给后端，统一存储，并且和 request id 区分开
            chat_response.request_id = response_id
            chat_response_data.request_receive_time = chat_request.request_receive_time
            chat_response_data.model_version = MODEL_VERSION
            if chat_response_data.selected_item is None:
                # 如果没有产生新的 selected_item，就用上一次的
                chat_response_data.selected_item = chat_request.ending_selected_item
            if chat_response.event == EventType.FINISH_EVENT:
                chat_response_data.answer_finish_time = get_cur_millis()
                chat_response_json = json.dumps(chat_response.to_dict(), indent=2, ensure_ascii=False)
                self._logger.debug(f"回答完成，回答内容：\n{chat_response_json}")
                if chat_request.version in (0, 1) and chat_request.debug:
                    chat_response_data.time_cost = self.get_execution_time_summary()
                chat_response_data.total_tokens = self.get_total_token_usage()
                self.insert_to_db(chat_request, chat_response, first_token_elapse)
                if chat_request is None or not chat_request.debug:
                    # 前端要求最后一个 FINISH_EVENT text 为空串（为了和中国区逻辑兼容）
                    chat_response_data.text = ""
            # to SSE str, starts with "data:"
            yield encode_sse(chat_response)

    async def chat_inner(self, chat_request: ChatRequest, http_request: Request):
        """子类需要实现此方法"""
        raise NotImplementedError("子类必须实现 chat_inner 方法")

    def insert_to_db(self, chat_request, finish_chat_response, first_token_elapse):
        try:
            start_time = time.time()
            insert_record = DBRecord(
                conversation_id=chat_request.conversation_id,
                question_id=chat_request.request_id,
                add_time=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                question_content=chat_request.ending_message.content if hasattr(chat_request.ending_message, "content") else chat_request.ending_message,
                response=finish_chat_response.data.text,
                first_token_elapse=first_token_elapse,
                request_receive_time=finish_chat_response.data.request_receive_time,
                answer_start_time=finish_chat_response.data.answer_start_time,
                answer_finish_time=finish_chat_response.data.answer_finish_time,
                total_tokens=finish_chat_response.data.total_tokens,
                answer_type=finish_chat_response.data.answer_type,
                intents=self._answer_intent,
                selected_item_names=chat_request.item_name,
                actual_item_names=",".join(self._actual_item_name_list),
                item_attributes=",".join(self._second_tags),
                model_version=MODEL_VERSION,
                dt=datetime.datetime.now().strftime('%Y%m%d'),
                chat_request=to_json_str(chat_request),
                prompt=self._user_prompt,
                system_language=chat_request.language.value,
                input_language=chat_request.recognize_language.value if chat_request.recognize_language is not None else chat_request.language.value,
                output_language=chat_request.recognize_language.value if chat_request.recognize_language is not None else chat_request.language.value,
                area=chat_request.area.value,
                version=chat_request.version,
                response_id=finish_chat_response.request_id,
            ).to_dict()
            self._logger.debug(f"写入数据库：{insert_record}")
            self._db.insert_record(self._data_table_name, insert_record)
            self._logger.debug(f"insert_data: {time.time() - start_time}")
        except Exception as e:
            self._logger.error(f"写入数据库发生错误 {str(e)}: {traceback.format_exc()}")

    def refuse_answer(self, refuse_type: MessageType):
        # 更新统计
        self._counter.labels(object=CHAT, condition=refuse_type.name).inc()
        # 生成并返回拒绝回答的事件
        refusal_message = REFUSAL_MESSAGE_DICT[refuse_type]
        for chat_response in self.wrap_str_to_response_stream(refusal_message, answer_type=refuse_type):
            yield chat_response

    async def cancel_tasks(self, task_list):
        for task in task_list:
            if task is None:
                continue

            if task.done():
                self._logger.debug(f"{task.get_name()} 无需取消或已结束")
                continue

            task.cancel()
            try:
                await task
            except (asyncio.CancelledError, StopAsyncIteration):
                self._logger.debug(f"{task.get_name()} 已取消")
            except Exception as e:
                self._logger.error(f"{task.get_name()} 取消时出错: {e}")

    async def execute_knowledge_retrieval(self, ending_msg, second_tag_map_dict, item_name_normalized,
                                          task_group: TaskGroup):
        self._task_retrieval_doc = task_group.create_task(
            self._prompt_build_service.retrieve_doc_knowledge(ending_msg, second_tag_map_dict, item_name_normalized),
            name="知识检索任务_DOC",
        )
        return {
            KnowledgeSource.DOC: self._task_retrieval_doc,
        }

    async def get_retrieval_results(self, tasks):
        # 等待所有任务完成并获取结果
        knowledge_dict = {}
        for source, task in tasks.items():
            try:
                knowledge_dict[source] = await task
            except Exception as e:
                self._logger.error(
                    f"{task.get_name()}失败，prompt 构建时信息可能缺失 {str(e)}: {traceback.format_exc()}")
        return knowledge_dict

    async def generate_response_stream(self, user_prompt, chat_request, http_request, answer_type,
                                       need_first_event=True, pre_thinking_str=""):
        start_time = time.time()
        is_first_chunk = True
        async for response in self._model_manager.call_llm_with_stream(user_prompt, self._api_key_text,
                                                                       chat_request.request_id):
            if await http_request.is_disconnected():
                raise RuntimeError("客户端已断开连接")

            llm_response_data_dict = decode_sse(response.decode("utf-8"))
            if is_empty(llm_response_data_dict) or "event" not in llm_response_data_dict:
                continue

            cur_event_type = llm_response_data_dict["event"]
            if cur_event_type == "text_chunk":
                if is_first_chunk and need_first_event:
                    # 追加 start event
                    chat_response = ChatResponse(
                        event=EventType.START_EVENT,
                        data=ChatResponseData(answer_type=answer_type)
                    )
                    is_first_chunk = False
                    yield chat_response

                chat_response = ChatResponse(
                    event=EventType.TEXT_CHUNK_EVENT,
                    data=ChatResponseData(
                        text=llm_response_data_dict["data"]["text"],
                        answer_type=answer_type
                    )
                )
                yield chat_response

            if cur_event_type == "workflow_finished":
                response_text = pre_thinking_str + llm_response_data_dict["data"]["outputs"]["answer"]
                response_data = ChatResponseData(text=response_text, answer_type=answer_type)
                if chat_request.debug:
                    response_data.prompt = user_prompt
                    response_data.total_tokens = llm_response_data_dict["data"]["total_tokens"]
                chat_response = ChatResponse(event=EventType.FINISH_EVENT, data=response_data)
                self._user_prompt = user_prompt
                self.record_execution_time('call_llm_with_stream', start_time)
                self.record_token_usage('call_llm_with_stream', llm_response_data_dict["data"]["total_tokens"])
                yield chat_response

    @staticmethod
    def wrap_str_to_response_stream(content, answer_type, selected_item=None, item_list=None, need_finish_event=True):
        chat_response = ChatResponse(
            event=EventType.START_EVENT,
            data=ChatResponseData(answer_type=answer_type, selected_item=selected_item, item_list=item_list)
        )
        yield chat_response

        chat_response = ChatResponse(
            event=EventType.TEXT_CHUNK_EVENT,
            data=ChatResponseData(text=content, answer_type=answer_type, selected_item=selected_item,
                                  item_list=item_list)
        )
        yield chat_response

        if need_finish_event:
            chat_response = ChatResponse(
                event=EventType.FINISH_EVENT,
                data=ChatResponseData(text=content, answer_type=answer_type, selected_item=selected_item,
                                      item_list=item_list)
            )
            yield chat_response

    def get_item_by_name(self, item_name):
        if is_empty(item_name):
            return None

        return self._name_item_dict.get(item_name, None)

    def categorize_item_names(self, recognize_query_item_name_list):
        """
        将识别到的机型名称分类为xiaomi和non_xiaomi。

        参数:
            recognize_query_item_name_list (list): 识别到的机型名称列表（已规范化）。

        返回:
            tuple: 包含两个列表，第一个是xiaomi机型，第二个是non_xiaomi机型。
        """
        item_names_xiaomi = []
        item_names_non_xiaomi = []
        for item_name in recognize_query_item_name_list:
            if item_name in self._item_name_xiaomi_list:
                item_names_xiaomi.append(item_name)
            else:
                item_names_non_xiaomi.append(item_name)
        return item_names_xiaomi, item_names_non_xiaomi

    def get_item_name_not_in_candidates(self, extracted_item_names):
        normalized_candidates = set(self._normalized_item_name_list)

        # 保留未在候选列表中的原始名称
        not_in_candidates = [
            name for name in extracted_item_names
            if normalize_item_name(name) not in normalized_candidates
        ]
        return not_in_candidates

    @staticmethod
    def get_pre_thinking_str(chat_request, second_tags_map_dict):
        language = chat_request.language.name.capitalize()
        if chat_request.recognize_language is not None:
            language = chat_request.recognize_language.name.capitalize()
        
        # item_name 不能为空
        if is_empty(chat_request.item_name):
            # 好的，让我来帮您解答。
            return random.sample(PRE_MESSAGE_DICT[language]["NO_ITEM_NAME"], 1)[0]

        if is_empty(second_tags_map_dict):
            # 好的，让我帮你回答你关于 item_name 的问题
            return random.sample(PRE_MESSAGE_DICT[language]["NO_SECOND_TAG"], 1)[0].format(chat_request.item_name)

        tag_str = ', '.join(second_tags_map_dict.values())
        # 好的，让我来帮您解答关于 item_name 的 x 方面的问题
        return random.sample(PRE_MESSAGE_DICT[language]["DEFAULT"], 1)[0].format(chat_request.item_name, tag_str)


def encode_sse(chat_response: ChatResponse):
    data = chat_response.to_dict()
    return f"data: {json.dumps(data)}\n\n"
